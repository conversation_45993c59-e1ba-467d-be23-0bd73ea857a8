#!/system/bin/sh

# 获取应用UID函数
get_app_uid() {
    dumpsys package "$1" 2>/dev/null | grep "userId=" | head -1 | cut -d'=' -f2
}

# 要绕过VPN的应用列表
BYPASS_APPS="com.tencent.mm mixiaba.com.Browser"

echo "设置VPN分流规则..."

# 为指定应用添加绕过规则（优先级高于11600）
for app in $BYPASS_APPS; do
    uid=$(get_app_uid "$app")
    if [ -n "$uid" ] && [ "$uid" != "" ]; then
        # 让这些应用走主路由表（绕过VPN）
        ip rule add from all uidrange "$uid-$uid" lookup main pref 11000
        echo "✓ $app (UID: $uid) 已设置绕过VPN"
    else
        echo "⚠ 应用 $app 未找到"
    fi
done

# 你的原始VPN规则（优先级最低，所以其他应用都走VPN）
echo "设置全局VPN规则..."
ip rule add from all fwmark 0x0/0x20000 uidrange 0-99999 lookup tun0 pref 11600
ip route add 0.0.0.0/0 dev tun0 table tun0

echo "✅ 完成！指定应用绕过VPN，其他应用走VPN"

# 显示结果
echo ""
echo "当前规则："
ip rule show | head -10
