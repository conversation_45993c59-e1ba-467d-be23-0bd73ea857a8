#!/system/bin/sh

echo "开始设置VPN分流规则..."

# 获取应用UID函数
get_app_uid() {
    dumpsys package "$1" 2>/dev/null | grep "userId=" | head -1 | cut -d'=' -f2
}

# 清理旧规则函数
cleanup_old_rules() {
    echo "清理旧的绕过规则..."
    # 删除可能存在的表200相关规则
    ip rule del table 200 2>/dev/null
    ip route flush table 200 2>/dev/null
}

# 主要设置函数
setup_vpn_bypass() {
    # 要绕过VPN的应用列表
    BYPASS_APPS="com.tencent.mm com.eg.android.AlipayGphone com.android.vending mixiaba.com.Browser"
    
    # 获取原始网络信息
    ORIGINAL_GW=$(ip route show table main | grep default | head -1 | awk '{print $3}')
    ORIGINAL_DEV=$(ip route show table main | grep default | head -1 | awk '{print $5}')
    
    if [ -z "$ORIGINAL_GW" ] || [ -z "$ORIGINAL_DEV" ]; then
        echo "错误: 无法获取原始网关信息"
        echo "当前路由表："
        ip route show table main
        return 1
    fi
    
    echo "原始网关: $ORIGINAL_GW, 设备: $ORIGINAL_DEV"
    
    # 直接使用数字表ID 200，不需要创建rt_tables文件
    # 设置绕过路由表（使用表200）
    ip route add default via $ORIGINAL_GW dev $ORIGINAL_DEV table 200
    
    if [ $? -eq 0 ]; then
        echo "✓ 绕过路由表设置成功"
    else
        echo "✗ 绕过路由表设置失败"
        return 1
    fi
    
    # 为每个应用添加绕过规则
    echo "添加应用绕过规则..."
    for app in $BYPASS_APPS; do
        uid=$(get_app_uid "$app")
        if [ -n "$uid" ] && [ "$uid" != "" ]; then
            # 使用表200而不是bypass
            ip rule add from all uidrange "$uid-$uid" table 200 pref 11000
            if [ $? -eq 0 ]; then
                echo "✓ $app (UID: $uid) 已添加绕过规则"
            else
                echo "✗ $app (UID: $uid) 绕过规则添加失败"
            fi
        else
            echo "⚠ 应用 $app 未安装或无法获取UID"
        fi
    done
    
    # 添加系统服务绕过
    echo "添加系统服务绕过规则..."
    ip rule add from all uidrange 1000-1000 table 200 pref 11100  # system
    ip rule add from all uidrange 1001-1001 table 200 pref 11101  # radio
    
    # 添加本地网络绕过
    echo "添加本地网络绕过规则..."
    ip rule add from all to ***********/16 table 200 pref 11200
    ip rule add from all to 10.0.0.0/8 table 200 pref 11201
    ip rule add from all to **********/12 table 200 pref 11202
    ip rule add from all to *********/8 table 200 pref 11203
    
    # 你的原始VPN规则（确保在最后，优先级最低）
    echo "设置全局VPN规则..."
    ip rule add from all fwmark 0x0/0x20000 uidrange 0-99999 lookup tun0 pref 11600
    ip route add 0.0.0.0/0 dev tun0 table tun0
    
    echo "✅ VPN分流规则设置完成！"
}

# 显示当前规则函数
show_rules() {
    echo "当前路由规则："
    ip rule show | head -20
    echo ""
    echo "表200路由内容："
    ip route show table 200
    echo ""
    echo "主路由表："
    ip route show table main
}

# 清理函数
cleanup_rules() {
    echo "清理所有绕过规则..."
    
    # 删除所有表200相关规则
    ip rule show | grep "lookup 200\|table 200" | while read line; do
        pref=$(echo "$line" | grep -o "pref [0-9]*" | cut -d' ' -f2)
        if [ -n "$pref" ]; then
            ip rule del pref "$pref" 2>/dev/null
            echo "删除规则: pref $pref"
        fi
    done
    
    # 清空表200
    ip route flush table 200 2>/dev/null
    
    echo "清理完成！"
}

# 主程序
case "$1" in
    "setup")
        cleanup_old_rules
        setup_vpn_bypass
        ;;
    "show")
        show_rules
        ;;
    "clean")
        cleanup_rules
        ;;
    *)
        echo "用法: $0 {setup|show|clean}"
        echo "  setup - 设置VPN分流规则"
        echo "  show  - 显示当前规则"
        echo "  clean - 清理所有规则"
        echo ""
        echo "直接运行默认执行setup..."
        cleanup_old_rules
        setup_vpn_bypass
        ;;
esac
